name: cloudtolocalllm
description: Manage and run powerful Large Language Models locally, orchestrated via a cloud interface.
publish_to: 'none'
homepage: https://cloudtolocalllm.online
repository: https://github.com/thrightguy/CloudToLocalLLM
version: 1.3.3+************

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  provider: ^6.1.1
  uuid: ^4.3.3
  path_provider: ^2.1.2
  shared_preferences: ^2.2.2
  url_launcher: ^6.2.4
  http: ^1.2.0
  openid_client: ^0.4.9
  auth0_flutter: ^1.3.1
  path: ^1.8.3
  process_run: ^1.2.4
  tray_manager: ^0.5.0
  window_manager: ^0.4.3
  win32: ^5.2.0
  logging: ^1.2.0 # Used by admin_control_daemon Dart server
  crypto: ^3.0.3
  device_info_plus: ^11.4.0
  package_info_plus: ^8.3.0
  flutter_secure_storage: ^10.0.0-beta.4
  args: ^2.4.2
  go_router: ^15.1.2
  flutter_platform_alert: ^0.7.0
  flutter_acrylic: ^1.1.4
  web: ^1.1.1
  mcp_toolkit: ^0.1.2
  # Removed Firebase dependencies in favor of Auth0

flutter:
  uses-material-design: true
  assets:
    - assets/images/CloudToLocalLLM_logo.jpg
