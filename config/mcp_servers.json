{"mcpServers": {"mcpadvisor": {"command": "node", "args": ["https://mcp.so/api/mcpadvisor"], "env": {"API_KEY": "wqhi62mb2ttvmf"}, "disabled": false}, "flutter-inspector-local": {"command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"PORT": "3334", "LOG_LEVEL": "critical", "RESOURCES_SUPPORTED": "true", "IMAGES_SUPPORTED": "true", "DART_VM_HOST": "localhost", "DART_VM_PORT": "8182", "DDS_HOST": "localhost", "DDS_PORT": "8181", "REMOTE_MODE": "false"}, "disabled": false}, "flutter-inspector-remote": {"command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"PORT": "3335", "LOG_LEVEL": "critical", "RESOURCES_SUPPORTED": "true", "IMAGES_SUPPORTED": "true", "DART_VM_HOST": "localhost", "DART_VM_PORT": "18182", "DDS_HOST": "localhost", "DDS_PORT": "18181", "REMOTE_MODE": "true", "TARGET_HOST": "cloudtolocalllm.online"}, "disabled": true}}}