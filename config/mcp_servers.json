{"servers": {"mcpadvisor": {"type": "http", "url": "https://mcp.so/api/mcpadvisor", "auth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "key": "wqhi62mb2ttvmf"}}, "flutter_mcp_server": {"type": "http", "url": "https://mcp.so/api/flutter_mcp_server", "command": "docker run -p 8080:8080 centinolalt/flutter_mcp_server", "tools": [{"name": "analyze", "description": "Analyze Dart/Flutter files for diagnostics and suggestions", "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "Path to the Dart/Flutter file to analyze"}}, "required": ["filePath"]}}]}, "flutter-inspector-local": {"type": "stdio", "command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"PORT": "3334", "LOG_LEVEL": "critical", "RESOURCES_SUPPORTED": "true", "IMAGES_SUPPORTED": "true", "DART_VM_HOST": "localhost", "DART_VM_PORT": "8182", "DDS_HOST": "localhost", "DDS_PORT": "8181", "REMOTE_MODE": "false"}, "disabled": false, "description": "Local Flutter Inspector for development"}, "flutter-inspector-remote": {"type": "stdio", "command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"PORT": "3335", "LOG_LEVEL": "critical", "RESOURCES_SUPPORTED": "true", "IMAGES_SUPPORTED": "true", "DART_VM_HOST": "localhost", "DART_VM_PORT": "18182", "DDS_HOST": "localhost", "DDS_PORT": "18181", "REMOTE_MODE": "true", "TARGET_HOST": "cloudtolocalllm.online"}, "disabled": true, "description": "Remote Flutter Inspector for CloudToLocalLLM production app (requires SSH tunnel)"}}}