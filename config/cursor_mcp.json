{"mcpServers": {"flutter-inspector-local": {"command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"RESOURCES_SUPPORTED": "false", "IMAGES_SUPPORTED": "true", "LOG_LEVEL": "critical", "DART_VM_HOST": "localhost", "DART_VM_PORT": "8182", "DDS_HOST": "localhost", "DDS_PORT": "8181", "REMOTE_MODE": "false"}, "disabled": false}, "flutter-inspector-remote": {"command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"RESOURCES_SUPPORTED": "false", "IMAGES_SUPPORTED": "true", "LOG_LEVEL": "critical", "DART_VM_HOST": "localhost", "DART_VM_PORT": "18182", "DDS_HOST": "localhost", "DDS_PORT": "18181", "REMOTE_MODE": "true", "TARGET_HOST": "cloudtolocalllm.online"}, "disabled": true}}}