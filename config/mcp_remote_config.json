{"servers": {"flutter-inspector-remote": {"type": "stdio", "command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"PORT": "3334", "LOG_LEVEL": "critical", "RESOURCES_SUPPORTED": "true", "IMAGES_SUPPORTED": "true", "DART_VM_HOST": "localhost", "DART_VM_PORT": "18182", "DDS_HOST": "localhost", "DDS_PORT": "18181", "REMOTE_MODE": "true", "TARGET_HOST": "cloudtolocalllm.online"}, "disabled": false, "description": "Remote Flutter Inspector for CloudToLocalLLM production app"}, "flutter-inspector-local": {"type": "stdio", "command": "node", "args": ["/home/<USER>/Dev/Tools/MCP/mcp_flutter/mcp_server/build/index.js"], "env": {"PORT": "3335", "LOG_LEVEL": "critical", "RESOURCES_SUPPORTED": "true", "IMAGES_SUPPORTED": "true", "DART_VM_HOST": "localhost", "DART_VM_PORT": "8182", "DDS_HOST": "localhost", "DDS_PORT": "8181", "REMOTE_MODE": "false"}, "disabled": false, "description": "Local Flutter Inspector for development"}}, "connection_info": {"remote": {"description": "Connection to production CloudToLocalLLM app via SSH tunnel", "vm_service": "http://localhost:18182", "dds_service": "http://localhost:18181", "target_url": "https://cloudtolocalllm.online", "tunnel_required": true, "setup_command": "scripts/setup_mcp_tunnel.sh", "cleanup_command": "scripts/stop_mcp_tunnel.sh"}, "local": {"description": "Connection to local development Flutter app", "vm_service": "http://localhost:8182", "dds_service": "http://localhost:8181", "target_url": "http://localhost:3000", "tunnel_required": false, "setup_command": "./flutter/bin/flutter run --debug --host-vmservice-port=8182 --dds-port=8181 --enable-vm-service --disable-service-auth-codes"}}, "security_notes": ["Remote debugging exposes Flutter VM services on the production server", "Use SSH tunnels for secure access to remote debug services", "Always run cleanup scripts after debugging sessions", "Monitor firewall rules and close debug ports when not needed", "Consider IP whitelisting for additional security"]}